import { useState, useEffect } from 'react'
import {
  FiEdit, FiImage, FiPackage, FiCalendar, FiShoppingBag, FiUsers,
  FiUserCheck, FiBarChart, FiSettings, FiHome, FiPlus, FiRefreshCw, FiSave, FiUpload, FiX, FiMail
} from 'react-icons/fi'
import BrandingModal from '../../../components/Modals/BrandingModal'
import EmailTemplateModal from '../../../components/Modals/EmailTemplateModal'
import { adminService } from '../../../services'
import { brandingService } from '../../../services/brandingService'
import uploadService from '../../../services/uploadService'
import { useBranding } from '../../../contexts/BrandingContext'

const AdminBranding = ({
  showToast,
  branding
}) => {
  const { refreshBranding } = useBranding()
  const [brandingData, setBrandingData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [emailTemplates, setEmailTemplates] = useState([])
  const [loadingTemplates, setLoadingTemplates] = useState(false)
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [uploadingImages, setUploadingImages] = useState({})
  const [dataSource, setDataSource] = useState(null) // 'cache' or 'api'
  const [showEmailTemplateModal, setShowEmailTemplateModal] = useState(false)
  const [editingEmailTemplate, setEditingEmailTemplate] = useState(null)

  useEffect(() => {
    loadBrandingData()
    loadEmailTemplates()
  }, [])

  // Load email templates
  const loadEmailTemplates = async () => {
    try {
      setLoadingTemplates(true)
      const response = await adminService.getAllEmailTemplates()
      if (response.success && response.data) {
        setEmailTemplates(response.data)
      }
    } catch (error) {
      console.error('Error loading email templates:', error)
      showToast('Failed to load email templates', 'error')
    } finally {
      setLoadingTemplates(false)
    }
  }

  const loadBrandingData = async (forceRefresh = false) => {
    try {
      setLoading(true)
      console.log('Loading branding data...', forceRefresh ? '(force refresh)' : '(with cache)')
      const response = await brandingService.getBranding(forceRefresh)
      console.log('Branding response:', response)

      if (response.fromCache) {
        console.log('✅ Loaded from cache (30min TTL)')
        setDataSource('cache')
      } else {
        console.log('🌐 Loaded from API')
        setDataSource('api')
      }

      if (response.success && response.data) {
        // Structure the data for editing - using nested structure from /api/branding
        const { global, home, services, shop, consultation, login, signup, cart, productDetail, footer, dashboard, buttons, navigation, testimonials, reviews, messages, business, theme, site, legal } = response.data
        console.log('Global data:', global)
        console.log('Business data:', business)
        console.log('Theme data:', theme)

        setBrandingData({
          // Global/Site-wide Information
          global: {
            siteName: global?.siteName || business?.name || '',
            tagline: global?.tagline || business?.tagline || '',
            phone: global?.phone || business?.phone || '',
            email: global?.email || business?.email || '',
            address: global?.address || business?.address?.full || '',
            logo: global?.logo || '',
            favicon: global?.favicon || '',
            instagram: global?.instagram || business?.social?.instagram || '',
            facebook: global?.facebook || business?.social?.facebook || '',
            twitter: global?.twitter || business?.social?.twitter || '',
            youtube: global?.youtube || ''
          },

          // Theme Settings
          theme: {
            colors: {
              primary: theme?.colors?.primary || '',
              secondary: theme?.colors?.secondary || '',
              accent: theme?.colors?.accent || '',
              background: theme?.colors?.background || '',
              text: theme?.colors?.text || '',
              textSecondary: theme?.colors?.textSecondary || ''
            },
            fonts: {
              primary: theme?.fonts?.primary || '',
              secondary: theme?.fonts?.secondary || '',
              heading: theme?.fonts?.heading || ''
            }
          },

          // Home Page Content (from nested structure)
          home: {
            heroTitle: home?.heroTitle || '',
            heroSubtitle: home?.heroSubtitle || '',
            heroImage: home?.heroImage || '',
            aboutTitle: home?.aboutTitle || '',
            aboutText: home?.aboutText || '',
            featuredServices: home?.featuredServices || [],
            testimonialHeading: home?.testimonialHeading || ''
          },

          // Services Content (from nested structure)
          services: {
            pageTitle: services?.pageTitle || '',
            pageSubtitle: services?.pageSubtitle || '',
            pageDescription: services?.pageDescription || '',
            serviceLocMaintenance: services?.serviceLocMaintenance || '',
            serviceLocMaintenanceDesc: services?.serviceLocMaintenanceDesc || '',
            serviceStarterLocs: services?.serviceStarterLocs || '',
            serviceStarterLocsDesc: services?.serviceStarterLocsDesc || '',
            serviceLocStyling: services?.serviceLocStyling || '',
            serviceLocStylingDesc: services?.serviceLocStylingDesc || '',
            serviceNaturalHairCare: services?.serviceNaturalHairCare || '',
            serviceNaturalHairCareDesc: services?.serviceNaturalHairCareDesc || '',
            serviceCategories: services?.serviceCategories || []
          },

          // Shop Content (from nested structure)
          shop: {
            pageTitle: shop?.pageTitle || '',
            pageSubtitle: shop?.pageSubtitle || '',
            pageDescription: shop?.pageDescription || '',
            featuredCollectionTitle: shop?.featuredCollectionTitle || ''
          },

          // Consultation Content (from nested structure)
          consultation: {
            pageTitle: consultation?.pageTitle || '',
            pageSubtitle: consultation?.pageSubtitle || '',
            pageDescription: consultation?.pageDescription || '',
            formTitle: consultation?.formTitle || '',
            formSubtitle: consultation?.formSubtitle || ''
          },

          // Login Content (from nested structure)
          login: {
            pageTitle: login?.pageTitle || '',
            pageSubtitle: login?.pageSubtitle || ''
          },

          // Signup Content (from nested structure)
          signup: {
            pageTitle: signup?.pageTitle || '',
            pageSubtitle: signup?.pageSubtitle || '',
            agreeTermsText: signup?.agreeTermsText || '',
            termsLinkText: signup?.termsLinkText || '',
            andText: signup?.andText || '',
            privacyLinkText: signup?.privacyLinkText || ''
          },

          // Cart Content (from nested structure)
          cart: {
            pageTitle: cart?.pageTitle || '',
            emptyCartMessage: cart?.emptyCartMessage || '',
            freeShippingThreshold: cart?.freeShippingThreshold || '',
            shippingCalculated: cart?.shippingCalculated || ''
          },



          // Dashboard Content (from nested structure)
          dashboard: {
            dashboardWelcome: dashboard?.welcomeMessage || '',
            dashboardOverviewTitle: dashboard?.overviewTitle || '',
            dashboardAppointmentsTitle: dashboard?.appointmentsTitle || '',
            dashboardOrdersTitle: dashboard?.ordersTitle || '',
            dashboardFavoritesTitle: dashboard?.favoritesTitle || '',
            dashboardProfileTitle: dashboard?.profileTitle || '',
            dashboardNextAppointment: dashboard?.nextAppointment || '',
            dashboardRecentOrders: dashboard?.recentOrders || '',
            dashboardLoyaltyTitle: dashboard?.loyaltyTitle || ''
          },

          // Buttons (from nested structure)
          buttons: {
            bookNowButton: buttons?.bookNow || '',
            shopNowButton: buttons?.shopNow || '',
            learnMoreButton: buttons?.learnMore || '',
            viewAllButton: buttons?.viewAll || '',
            continueShoppingButton: buttons?.continueShopping || '',
            proceedToCheckoutButton: buttons?.proceedToCheckout || '',
            addToCartButton: buttons?.addToCart || '',
            scheduleConsultationButton: buttons?.scheduleConsultation || '',
            writeReviewButton: buttons?.writeReview || ''
          },

          // Navigation (from nested structure)
          navigation: {
            navHome: navigation?.home || '',
            navServices: navigation?.services || '',
            navShop: navigation?.shop || '',
            navConsultation: navigation?.consultation || '',
            navLogin: navigation?.login || '',
            navSignup: navigation?.signup || '',
            navDashboard: navigation?.dashboard || ''
          },

          // Testimonials (from nested structure)
          testimonials: {
            testimonialsTitle: testimonials?.title || '',
            testimonialsSubtitle: testimonials?.subtitle || ''
          },

          // Reviews (from nested structure)
          reviews: {
            reviewsTitle: reviews?.title || ''
          },

          // Messages (from nested structure)
          messages: {
            loadingMessage: messages?.loading || '',
            errorMessage: messages?.error || '',
            notFoundMessage: messages?.notFound || '',
            comingSoonMessage: messages?.comingSoon || '',
            cartShippingMessage: messages?.cartShipping || ''
          },

          // Footer Content (from nested structure)
          footer: {
            description: footer?.description || '',
            copyrightText: footer?.copyrightText || '',
            contact: footer?.contact || '',
            followUs: footer?.followUs || ''
          },

          // Business Information (from business object)
          business: business || {},

          // Site Settings (from site object)
          site: site || {},

          // Legal Content (Privacy Policy and Terms of Service)
          legal: {
            privacyPolicy: {
              title: legal?.privacyPolicy?.title || 'Privacy Policy',
              content: legal?.privacyPolicy?.content || '',
              lastUpdated: legal?.privacyPolicy?.lastUpdated || new Date()
            },
            termsOfService: {
              title: legal?.termsOfService?.title || 'Terms of Service',
              content: legal?.termsOfService?.content || '',
              lastUpdated: legal?.termsOfService?.lastUpdated || new Date()
            },
            copyrightText: legal?.copyrightText || '',
            companyName: legal?.companyName || ''
          }
        })

        console.log('Structured branding data set successfully')
      } else {
        throw new Error('Failed to load complete branding data')
      }
    } catch (error) {
      console.error('Error loading branding data:', error)
      showToast('Error loading branding data', 'error')
      setBrandingData({})
    } finally {
      setLoading(false)
    }
  }

  const handleSaveBranding = async (formData) => {
    try {
      setSaving(true)

      // Transform the form data to match the backend Branding model structure
      const brandingPayload = {
        global: {
          siteName: formData.global?.siteName || '',
          tagline: formData.global?.tagline || '',
          phone: formData.global?.phone || '',
          email: formData.global?.email || '',
          address: formData.global?.address || '',
          logo: formData.global?.logo || '',
          favicon: formData.global?.favicon || '',
          // Social media fields go directly in global for backend model
          instagram: formData.social?.instagram || '',
          facebook: formData.social?.facebook || '',
          twitter: formData.social?.twitter || '',
          youtube: formData.social?.youtube || ''
        },
        home: {
          heroTitle: formData.home?.heroTitle || '',
          heroSubtitle: formData.home?.heroSubtitle || '',
          heroImage: formData.home?.heroImage || '',
          aboutTitle: formData.home?.aboutTitle || '',
          aboutText: formData.home?.aboutText || '',
          featuredServices: formData.home?.featuredServices || [],
          testimonialHeading: formData.home?.testimonialHeading || ''
        },
        services: {
          pageTitle: formData.services?.servicesTitle || '',
          pageSubtitle: formData.services?.servicesSubtitle || '',
          pageDescription: formData.services?.servicesDescription || '',
          serviceCategories: formData.services?.serviceCategories || [],
          serviceLocMaintenance: formData.services?.serviceLocMaintenance || '',
          serviceLocMaintenanceDesc: formData.services?.serviceLocMaintenanceDesc || '',
          serviceStarterLocs: formData.services?.serviceStarterLocs || '',
          serviceStarterLocsDesc: formData.services?.serviceStarterLocsDesc || '',
          serviceLocStyling: formData.services?.serviceLocStyling || '',
          serviceLocStylingDesc: formData.services?.serviceLocStylingDesc || '',
          serviceNaturalHairCare: formData.services?.serviceNaturalHairCare || '',
          serviceNaturalHairCareDesc: formData.services?.serviceNaturalHairCareDesc || ''
        },
        shop: {
          pageTitle: formData.shop?.shopTitle || '',
          pageSubtitle: formData.shop?.shopSubtitle || '',
          pageDescription: formData.shop?.shopDescription || '',
          featuredCollectionTitle: formData.shop?.shopFeaturedTitle || ''
        },
        consultation: {
          pageTitle: formData.consultation?.consultationTitle || '',
          pageSubtitle: formData.consultation?.consultationSubtitle || '',
          pageDescription: formData.consultation?.consultationDescription || '',
          formTitle: formData.consultation?.consultationFormTitle || '',
          formSubtitle: formData.consultation?.consultationFormSubtitle || ''
        },
        login: {
          pageTitle: formData.auth?.loginTitle || '',
          pageSubtitle: formData.auth?.loginSubtitle || ''
        },
        signup: {
          pageTitle: formData.auth?.signupTitle || '',
          pageSubtitle: formData.auth?.signupSubtitle || '',
          agreeTermsText: formData.auth?.agreeTermsText || '',
          termsLinkText: formData.auth?.termsLinkText || '',
          andText: formData.auth?.andText || '',
          privacyLinkText: formData.auth?.privacyLinkText || ''
        },
        cart: {
          pageTitle: formData.cart?.cartTitle || '',
          emptyCartMessage: formData.cart?.emptyCartMessage || '',
          freeShippingThreshold: formData.cart?.freeShippingThreshold || '',
          shippingCalculated: formData.cart?.shippingCalculated || ''
        },
        productDetail: {
          addToCartButton: formData.productDetail?.addToCartButton || '',
          quantityLabel: formData.productDetail?.quantityLabel || '',
          overviewTab: formData.productDetail?.overviewTab || '',
          ingredientsTab: formData.productDetail?.ingredientsTab || '',
          reviewsTab: formData.productDetail?.reviewsTab || ''
        },
        footer: {
          description: formData.footer?.footerDescription || '',
          copyrightText: formData.footer?.footerCopyright || '',
          quickLinks: formData.footer?.quickLinks || [],
          contact: formData.footer?.contact || '',
          followUs: formData.footer?.followUs || ''
        },
        // Dashboard content
        dashboard: {
          welcomeMessage: formData.dashboard?.dashboardWelcome || '',
          overviewTitle: formData.dashboard?.dashboardOverviewTitle || '',
          appointmentsTitle: formData.dashboard?.dashboardAppointmentsTitle || '',
          ordersTitle: formData.dashboard?.dashboardOrdersTitle || '',
          favoritesTitle: formData.dashboard?.dashboardFavoritesTitle || '',
          profileTitle: formData.dashboard?.dashboardProfileTitle || '',
          nextAppointment: formData.dashboard?.dashboardNextAppointment || '',
          recentOrders: formData.dashboard?.dashboardRecentOrders || '',
          loyaltyTitle: formData.dashboard?.dashboardLoyaltyTitle || ''
        },
        // Buttons
        buttons: {
          bookNow: formData.buttons?.bookNowButton || '',
          shopNow: formData.buttons?.shopNowButton || '',
          learnMore: formData.buttons?.learnMoreButton || '',
          viewAll: formData.buttons?.viewAllButton || '',
          continueShopping: formData.buttons?.continueShoppingButton || '',
          proceedToCheckout: formData.buttons?.proceedToCheckoutButton || '',
          addToCart: formData.buttons?.addToCartButton || '',
          scheduleConsultation: formData.buttons?.scheduleConsultationButton || '',
          writeReview: formData.buttons?.writeReviewButton || ''
        },
        // Navigation
        navigation: {
          home: formData.navigation?.navHome || '',
          services: formData.navigation?.navServices || '',
          shop: formData.navigation?.navShop || '',
          consultation: formData.navigation?.navConsultation || '',
          login: formData.navigation?.navLogin || '',
          signup: formData.navigation?.navSignup || '',
          dashboard: formData.navigation?.navDashboard || ''
        },
        // Testimonials
        testimonials: {
          title: formData.testimonials?.testimonialsTitle || '',
          subtitle: formData.testimonials?.testimonialsSubtitle || ''
        },
        // Reviews
        reviews: {
          title: formData.reviews?.reviewsTitle || ''
        },
        // Messages
        messages: {
          loading: formData.messages?.loadingMessage || '',
          error: formData.messages?.errorMessage || '',
          notFound: formData.messages?.notFoundMessage || '',
          comingSoon: formData.messages?.comingSoonMessage || '',
          cartShipping: formData.messages?.cartShippingMessage || ''
        },
        // Business information
        business: {
          name: formData.business?.name || '',
          tagline: formData.business?.tagline || '',
          description: formData.business?.description || '',
          phone: formData.business?.phone || '',
          email: formData.business?.email || '',
          address: formData.business?.address || {},
          social: formData.business?.social || {},
          hours: formData.business?.hours || {}
        },
        // Theme settings
        theme: {
          colors: formData.theme?.colors || {},
          fonts: formData.theme?.fonts || {}
        },
        // Site settings
        site: {
          seo: formData.site?.seo || {},
          features: formData.site?.features || {}
        },
        // Legal content
        legal: {
          privacyPolicy: {
            title: formData.legal?.privacyPolicy?.title || 'Privacy Policy',
            content: formData.legal?.privacyPolicy?.content || '',
            lastUpdated: new Date()
          },
          termsOfService: {
            title: formData.legal?.termsOfService?.title || 'Terms of Service',
            content: formData.legal?.termsOfService?.content || '',
            lastUpdated: new Date()
          },
          copyrightText: formData.legal?.copyrightText || '',
          companyName: formData.legal?.companyName || ''
        }
      }

      console.log('Sending branding payload:', brandingPayload)
      const response = await adminService.updateBranding(brandingPayload)
      console.log('Branding update response:', response)

      if (response.success) {
        setBrandingData(formData)
        setHasUnsavedChanges(false)
        showToast('Branding updated successfully!', 'success')

        // Clear cache and reload fresh data
        brandingService.clearCache()

        // Trigger immediate real-time update across all tabs/windows
        localStorage.setItem('branding_updated', Date.now().toString())

        // Refresh branding context to apply changes immediately
        await refreshBranding()

        // Reload fresh data from API
        await loadBrandingData(true) // Force refresh
      } else {
        throw new Error(response.message || 'Failed to update branding')
      }
    } catch (error) {
      console.error('Error saving branding:', error)
      showToast(`Error saving branding: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveChanges = async () => {
    if (!hasUnsavedChanges || !brandingData) return

    try {
      setSaving(true)

      // Transform the form data to match the backend Branding model structure
      const brandingPayload = {
        global: {
          siteName: brandingData.global?.siteName || '',
          tagline: brandingData.global?.tagline || '',
          phone: brandingData.global?.phone || '',
          email: brandingData.global?.email || '',
          address: brandingData.global?.address || '',
          logo: brandingData.global?.logo || '',
          favicon: brandingData.global?.favicon || '',
          // Social media fields go directly in global for backend model
          instagram: brandingData.social?.instagram || '',
          facebook: brandingData.social?.facebook || '',
          twitter: brandingData.social?.twitter || '',
          youtube: brandingData.social?.youtube || ''
        },
        home: {
          heroTitle: brandingData.home?.heroTitle || '',
          heroSubtitle: brandingData.home?.heroSubtitle || '',
          heroImage: brandingData.home?.heroImage || '',
          aboutTitle: brandingData.home?.aboutTitle || '',
          aboutText: brandingData.home?.aboutText || '',
          featuredServices: brandingData.home?.featuredServices || [],
          testimonialHeading: brandingData.home?.testimonialHeading || ''
        },
        services: {
          pageTitle: brandingData.services?.servicesTitle || '',
          pageSubtitle: brandingData.services?.servicesSubtitle || '',
          pageDescription: brandingData.services?.servicesDescription || '',
          serviceCategories: brandingData.services?.serviceCategories || [],
          serviceLocMaintenance: brandingData.services?.serviceLocMaintenance || '',
          serviceLocMaintenanceDesc: brandingData.services?.serviceLocMaintenanceDesc || '',
          serviceStarterLocs: brandingData.services?.serviceStarterLocs || '',
          serviceStarterLocsDesc: brandingData.services?.serviceStarterLocsDesc || '',
          serviceLocStyling: brandingData.services?.serviceLocStyling || '',
          serviceLocStylingDesc: brandingData.services?.serviceLocStylingDesc || '',
          serviceNaturalHairCare: brandingData.services?.serviceNaturalHairCare || '',
          serviceNaturalHairCareDesc: brandingData.services?.serviceNaturalHairCareDesc || ''
        },
        shop: {
          pageTitle: brandingData.shop?.shopTitle || '',
          pageSubtitle: brandingData.shop?.shopSubtitle || '',
          pageDescription: brandingData.shop?.shopDescription || '',
          featuredCollectionTitle: brandingData.shop?.shopFeaturedTitle || ''
        },
        consultation: {
          pageTitle: brandingData.consultation?.consultationTitle || '',
          pageSubtitle: brandingData.consultation?.consultationSubtitle || '',
          pageDescription: brandingData.consultation?.consultationDescription || '',
          formTitle: brandingData.consultation?.consultationFormTitle || '',
          formSubtitle: brandingData.consultation?.consultationFormSubtitle || ''
        },
        login: {
          pageTitle: brandingData.auth?.loginTitle || '',
          pageSubtitle: brandingData.auth?.loginSubtitle || ''
        },
        signup: {
          pageTitle: brandingData.auth?.signupTitle || '',
          pageSubtitle: brandingData.auth?.signupSubtitle || '',
          agreeTermsText: brandingData.auth?.agreeTermsText || '',
          termsLinkText: brandingData.auth?.termsLinkText || '',
          andText: brandingData.auth?.andText || '',
          privacyLinkText: brandingData.auth?.privacyLinkText || ''
        },
        cart: {
          pageTitle: brandingData.cart?.cartTitle || '',
          emptyCartMessage: brandingData.cart?.emptyCartMessage || '',
          freeShippingThreshold: brandingData.cart?.freeShippingThreshold || '',
          shippingCalculated: brandingData.cart?.shippingCalculated || ''
        },
        productDetail: {
          addToCartButton: brandingData.productDetail?.addToCartButton || '',
          quantityLabel: brandingData.productDetail?.quantityLabel || '',
          overviewTab: brandingData.productDetail?.overviewTab || '',
          ingredientsTab: brandingData.productDetail?.ingredientsTab || '',
          reviewsTab: brandingData.productDetail?.reviewsTab || ''
        },
        footer: {
          description: brandingData.footer?.footerDescription || '',
          copyrightText: brandingData.footer?.footerCopyright || '',
          quickLinks: brandingData.footer?.quickLinks || [],
          contact: brandingData.footer?.contact || '',
          followUs: brandingData.footer?.followUs || ''
        },
        // Dashboard content
        dashboard: {
          welcomeMessage: brandingData.dashboard?.dashboardWelcome || '',
          overviewTitle: brandingData.dashboard?.dashboardOverviewTitle || '',
          appointmentsTitle: brandingData.dashboard?.dashboardAppointmentsTitle || '',
          ordersTitle: brandingData.dashboard?.dashboardOrdersTitle || '',
          favoritesTitle: brandingData.dashboard?.dashboardFavoritesTitle || '',
          profileTitle: brandingData.dashboard?.dashboardProfileTitle || '',
          nextAppointment: brandingData.dashboard?.dashboardNextAppointment || '',
          recentOrders: brandingData.dashboard?.dashboardRecentOrders || '',
          loyaltyTitle: brandingData.dashboard?.dashboardLoyaltyTitle || ''
        },
        // Buttons
        buttons: {
          bookNow: brandingData.buttons?.bookNowButton || '',
          shopNow: brandingData.buttons?.shopNowButton || '',
          learnMore: brandingData.buttons?.learnMoreButton || '',
          viewAll: brandingData.buttons?.viewAllButton || '',
          continueShopping: brandingData.buttons?.continueShoppingButton || '',
          proceedToCheckout: brandingData.buttons?.proceedToCheckoutButton || '',
          addToCart: brandingData.buttons?.addToCartButton || '',
          scheduleConsultation: brandingData.buttons?.scheduleConsultationButton || '',
          writeReview: brandingData.buttons?.writeReviewButton || ''
        },
        // Navigation
        navigation: {
          home: brandingData.navigation?.navHome || '',
          services: brandingData.navigation?.navServices || '',
          shop: brandingData.navigation?.navShop || '',
          consultation: brandingData.navigation?.navConsultation || '',
          login: brandingData.navigation?.navLogin || '',
          signup: brandingData.navigation?.navSignup || '',
          dashboard: brandingData.navigation?.navDashboard || ''
        },
        // Testimonials
        testimonials: {
          title: brandingData.testimonials?.testimonialsTitle || '',
          subtitle: brandingData.testimonials?.testimonialsSubtitle || ''
        },
        // Reviews
        reviews: {
          title: brandingData.reviews?.reviewsTitle || ''
        },
        // Messages
        messages: {
          loading: brandingData.messages?.loadingMessage || '',
          error: brandingData.messages?.errorMessage || '',
          notFound: brandingData.messages?.notFoundMessage || '',
          comingSoon: brandingData.messages?.comingSoonMessage || '',
          cartShipping: brandingData.messages?.cartShippingMessage || ''
        },
        // Business information
        business: {
          name: brandingData.business?.name || '',
          tagline: brandingData.business?.tagline || '',
          description: brandingData.business?.description || '',
          phone: brandingData.business?.phone || '',
          email: brandingData.business?.email || '',
          address: brandingData.business?.address || {},
          social: brandingData.business?.social || {},
          hours: brandingData.business?.hours || {}
        },
        // Theme settings
        theme: {
          colors: brandingData.theme?.colors || {},
          fonts: brandingData.theme?.fonts || {}
        },
        // Site settings
        site: {
          seo: brandingData.site?.seo || {},
          features: brandingData.site?.features || {}
        },
        // Legal content
        legal: {
          privacyPolicy: {
            title: brandingData.legal?.privacyPolicy?.title || 'Privacy Policy',
            content: brandingData.legal?.privacyPolicy?.content || '',
            lastUpdated: new Date()
          },
          termsOfService: {
            title: brandingData.legal?.termsOfService?.title || 'Terms of Service',
            content: brandingData.legal?.termsOfService?.content || '',
            lastUpdated: new Date()
          },
          copyrightText: brandingData.legal?.copyrightText || '',
          companyName: brandingData.legal?.companyName || ''
        }
      }

      console.log('Saving branding changes:', brandingPayload)
      const response = await adminService.updateBranding(brandingPayload)
      console.log('Branding save response:', response)

      if (response.success) {
        setHasUnsavedChanges(false)
        showToast('Branding changes saved successfully!', 'success')

        // Trigger immediate real-time update across all tabs/windows
        localStorage.setItem('branding_updated', Date.now().toString())

        // Refresh branding context to apply changes immediately
        await refreshBranding()

        // Also reload the admin branding data to ensure consistency
        setTimeout(() => {
          loadBrandingData()
        }, 500)
      } else {
        throw new Error(response.message || 'Failed to save branding changes')
      }
    } catch (error) {
      console.error('Error saving branding changes:', error)
      showToast(`Error saving changes: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }

  // Helper function to convert RGB to hex
  const rgbToHex = (rgb) => {
    // Handle rgb(r, g, b) format
    const rgbMatch = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      const r = parseInt(rgbMatch[1]);
      const g = parseInt(rgbMatch[2]);
      const b = parseInt(rgbMatch[3]);
      return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
    }
    return rgb;
  };

  // Helper function to validate and format hex colors
  const validateHexColor = (color) => {
    if (!color) return '';

    // Remove any whitespace
    color = color.trim();

    // Convert RGB to hex if needed
    if (color.startsWith('rgb(')) {
      color = rgbToHex(color);
    }

    // If it doesn't start with #, add it
    if (!color.startsWith('#')) {
      color = '#' + color;
    }

    // Validate hex format (3 or 6 characters after #)
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (hexRegex.test(color)) {
      return color.toUpperCase(); // Return uppercase hex
    }

    return color; // Return as-is if invalid (let user fix it)
  };

  const renderInputField = (label, value, onChange, type = 'text', placeholder = '', rows = 1) => {
    const isTextarea = rows > 1
    const InputComponent = isTextarea ? 'textarea' : 'input'

    const handleChange = (inputValue) => {
      if (type === 'color') {
        // Validate and format hex colors
        const formattedColor = validateHexColor(inputValue);
        onChange(formattedColor);
      } else {
        onChange(inputValue);
      }
    };

    // For color inputs, use text input to ensure hex format
    const inputType = type === 'color' ? 'text' : type;

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
          {type === 'color' && (
            <span className="text-xs text-gray-500 ml-2">(Hex format: #RRGGBB)</span>
          )}
        </label>
        <div className="flex items-center space-x-2">
          <InputComponent
            type={isTextarea ? undefined : inputType}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder={placeholder || (type === 'color' ? '#008000' : '')}
            rows={isTextarea ? rows : undefined}
            pattern={type === 'color' ? '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' : undefined}
            title={type === 'color' ? 'Enter a hex color code (e.g., #008000)' : undefined}
          />
          {type === 'color' && value && (
            <div className="flex items-center space-x-2">
              <div
                className="w-10 h-10 rounded border border-gray-300 flex-shrink-0"
                style={{ backgroundColor: value }}
                title={`Color preview: ${value}`}
              />
              <input
                type="color"
                value={value}
                onChange={(e) => handleChange(e.target.value)}
                className="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                title="Click to open color picker"
              />
            </div>
          )}
        </div>
        {type === 'color' && value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value) && (
          <p className="text-red-500 text-xs mt-1">Please enter a valid hex color (e.g., #008000)</p>
        )}
      </div>
    )
  }

  const handleContentChange = (section, field, value) => {
    setBrandingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const handleNestedContentChange = (section, field, value) => {
    setBrandingData(prev => {
      const newData = { ...prev };

      // Handle nested field paths like 'colors.primary'
      if (field.includes('.')) {
        const fieldParts = field.split('.');
        let current = newData[section] = { ...newData[section] };

        // Navigate to the nested object
        for (let i = 0; i < fieldParts.length - 1; i++) {
          current[fieldParts[i]] = { ...current[fieldParts[i]] };
          current = current[fieldParts[i]];
        }

        // Set the final value
        current[fieldParts[fieldParts.length - 1]] = value;
      } else {
        // Handle simple field updates
        newData[section] = {
          ...newData[section],
          [field]: value
        };
      }

      return newData;
    });
    setHasUnsavedChanges(true);
  }

  // Image upload functionality with Cloudinary
  const handleImageUpload = async (section, field, file) => {
    const uploadKey = `${section}.${field}`;

    try {
      setUploadingImages(prev => ({ ...prev, [uploadKey]: true }));

      // Determine upload type based on field
      let uploadType = 'brandingImage'; // default

      if (field === 'logo' || field.includes('logo')) {
        uploadType = 'logo';
      } else if (field === 'favicon' || field.includes('favicon')) {
        uploadType = 'favicon';
      } else if (field === 'heroImage' || field.includes('heroImage')) {
        uploadType = 'heroImage';
      } else if (field.includes('image') || field.includes('Image')) {
        uploadType = 'brandingImage';
      }

      // Use Cloudinary upload with fallback to legacy upload
      let response;
      try {
        response = await uploadService.uploadFileToCloudinary(file, uploadType);
      } catch (cloudinaryError) {
        console.warn('Cloudinary upload failed, falling back to legacy upload:', cloudinaryError);
        response = await uploadService.uploadImage(file);
      }

      if (response.success && (response.data?.url || response.data?.imageUrl)) {
        const imageUrl = response.data.url || response.data.imageUrl;

        // Update the branding data with the new image URL
        if (field.includes('.')) {
          handleNestedContentChange(section, field, imageUrl);
        } else {
          handleContentChange(section, field, imageUrl);
        }

        showToast?.('Image uploaded successfully to cloud storage!', 'success');
      } else {
        throw new Error(response.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      showToast?.(`Upload failed: ${error.message}`, 'error');
    } finally {
      setUploadingImages(prev => ({ ...prev, [uploadKey]: false }));
    }
  };

  // Render image upload field
  const renderImageField = (label, value, section, field, description = '') => {
    const uploadKey = `${section}.${field}`;
    const isUploading = uploadingImages[uploadKey];

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
          {description && (
            <span className="text-xs text-gray-500 block">{description}</span>
          )}
        </label>

        <div className="space-y-3">
          {/* Current Image Preview */}
          {value && (
            <div className="relative inline-block">
              <img
                src={value.startsWith('http') ? value : `http://localhost:3000${value}`}
                alt={label}
                className="w-32 h-32 object-cover rounded-lg border border-gray-300"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
              <button
                type="button"
                onClick={() => {
                  if (field.includes('.')) {
                    handleNestedContentChange(section, field, '');
                  } else {
                    handleContentChange(section, field, '');
                  }
                }}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                title="Remove image"
              >
                <FiX className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Upload Button */}
          <div className="flex items-center space-x-2">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  handleImageUpload(section, field, file);
                }
                e.target.value = ''; // Reset input
              }}
              className="hidden"
              id={`upload-${section}-${field.replace('.', '-')}`}
              disabled={isUploading}
            />
            <label
              htmlFor={`upload-${section}-${field.replace('.', '-')}`}
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                isUploading
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FiUpload className="w-4 h-4 mr-2" />
              {isUploading ? 'Uploading...' : value ? 'Change Image' : 'Upload Image'}
            </label>

            {/* Manual URL Input */}
            <input
              type="url"
              value={value || ''}
              onChange={(e) => {
                if (field.includes('.')) {
                  handleNestedContentChange(section, field, e.target.value);
                } else {
                  handleContentChange(section, field, e.target.value);
                }
              }}
              placeholder="Or enter image URL"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
        </div>
      </div>
    );
  };

  // Handle email template editing
  const handleEditEmailTemplate = (template) => {
    setEditingEmailTemplate(template)
    setShowEmailTemplateModal(true)
  }

  // Handle email template save
  const handleSaveEmailTemplate = async (templateData) => {
    try {
      const response = await adminService.updateEmailTemplate(templateData.type, templateData)
      if (response.success) {
        showToast('Email template updated successfully!', 'success')
        loadEmailTemplates() // Reload templates
        setShowEmailTemplateModal(false)
        setEditingEmailTemplate(null)
      } else {
        throw new Error(response.message || 'Failed to update email template')
      }
    } catch (error) {
      console.error('Error updating email template:', error)
      showToast('Failed to update email template', 'error')
      throw error
    }
  }

  // Update email template
  const updateEmailTemplate = async (type, templateData) => {
    try {
      const response = await adminService.updateEmailTemplate(type, templateData)
      if (response.success) {
        showToast('Email template updated successfully!', 'success')
        loadEmailTemplates() // Reload templates
      } else {
        throw new Error(response.message || 'Failed to update email template')
      }
    } catch (error) {
      console.error('Error updating email template:', error)
      showToast('Failed to update email template', 'error')
    }
  }

  const sections = [
    { id: 'global', name: 'Global/Site-wide', icon: FiSettings },
    { id: 'theme', name: 'Theme & Colors', icon: FiImage },
    { id: 'home', name: 'Home Page', icon: FiHome },
    { id: 'services', name: 'Services Page', icon: FiPackage },
    { id: 'consultation', name: 'Consultation Page', icon: FiCalendar },
    { id: 'shop', name: 'Shop Page', icon: FiShoppingBag },
    { id: 'auth', name: 'Authentication Pages', icon: FiUsers },
    { id: 'footer', name: 'Footer', icon: FiSettings },
    { id: 'legal', name: 'Privacy & Terms', icon: FiUserCheck },
    { id: 'emails', name: 'Email Templates', icon: FiMail }
  ]

  // Use brandingData directly from API - no fallbacks
  // Show loading state while data is being fetched
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600">Loading branding data...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state if no data is available
  if (!brandingData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-gray-600 mb-4">Failed to load branding data</p>
            <button
              onClick={loadBrandingData}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors cursor-pointer"
            >
              <FiRefreshCw className="w-4 h-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Branding & Content Management</h2>
          <p className="text-gray-600 mt-1">
            Customize all text content, images, and branding elements across your website.
            Changes are automatically saved and will persist after page refresh.
          </p>
          {dataSource && (
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mt-2 ${
              dataSource === 'cache'
                ? 'bg-green-100 text-green-800'
                : 'bg-blue-100 text-blue-800'
            }`}>
              {dataSource === 'cache' ? '📦 Cached Data (30min TTL)' : '🌐 Fresh API Data'}
            </div>
          )}
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => loadBrandingData(false)}
            disabled={loading}
            className="flex items-center px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200 cursor-pointer disabled:opacity-50"
            title="Load from cache (30min TTL)"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Load Data
          </button>
          <button
            onClick={() => loadBrandingData(true)}
            disabled={loading}
            className="flex items-center px-4 py-3 bg-blue-100 text-blue-700 rounded-xl hover:bg-blue-200 transition-all duration-200 cursor-pointer disabled:opacity-50"
            title="Force refresh from API (bypass cache)"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Force Refresh
          </button>
          {hasUnsavedChanges && (
            <button
              onClick={handleSaveChanges}
              disabled={saving}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50"
            >
              <FiSave className={`w-4 h-4 mr-2 ${saving ? 'animate-pulse' : ''}`} />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
          <button
            onClick={() => setShowModal(true)}
            className="flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <FiEdit className="w-4 h-4 mr-2" />
            Edit Branding
          </button>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiEdit className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Branding Management Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p className="mb-2">Use this section to customize all text content across your website:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Global:</strong> Site name, contact info, and social media links</li>
                <li><strong>Home:</strong> Hero section, features, and call-to-action content</li>
                <li><strong>Services:</strong> Service descriptions and benefits</li>
                <li><strong>Shop:</strong> Product page headers and newsletter content</li>
                <li><strong>Consultation:</strong> Form labels and information text</li>
                <li><strong>Login/Signup:</strong> Authentication page content</li>
                <li><strong>Cart:</strong> Shopping cart and checkout text</li>
                <li><strong>Product Detail:</strong> Product page labels and sections</li>
                <li><strong>User Dashboard:</strong> Dashboard labels and messages</li>
                <li><strong>Footer:</strong> Footer description and section titles</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => {
            const Icon = section.icon
            return (
              <button
                key={section.id}
                onClick={() => {
                  setActiveBrandingSection(section.id)
                  // Scroll to top when switching sections for better UX
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                }}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                  activeBrandingSection === section.id
                    ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {section.name}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content Sections */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        {activeBrandingSection === 'global' && (
          <div className="space-y-6">
            {/* Site Information */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Site Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Site Name', brandingData?.global?.siteName || '', (value) => handleContentChange('global', 'siteName', value))}
                {renderInputField('Phone Number', brandingData?.global?.phone || '', (value) => handleContentChange('global', 'phone', value), 'tel')}
                {renderInputField('Email Address', brandingData?.global?.email || '', (value) => handleContentChange('global', 'email', value), 'email')}
                {renderInputField('Address', brandingData?.global?.address || '', (value) => handleContentChange('global', 'address', value), 'text', '', 3)}
              </div>
            </div>

            {/* Brand Assets */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Brand Assets</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {renderImageField('Logo', brandingData?.global?.logo || '', 'global', 'logo', 'Main site logo (recommended: 200x60px)')}
                {renderImageField('Favicon', brandingData?.global?.favicon || '', 'global', 'favicon', 'Browser tab icon (recommended: 32x32px)')}
              </div>
            </div>

            {/* Social Media */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Social Media</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Instagram URL', brandingData?.global?.instagram || '', (value) => handleContentChange('global', 'instagram', value), 'url', 'https://instagram.com/username')}
                {renderInputField('Facebook URL', brandingData?.global?.facebook || '', (value) => handleContentChange('global', 'facebook', value), 'url', 'https://facebook.com/page')}
                {renderInputField('Twitter URL', brandingData?.global?.twitter || '', (value) => handleContentChange('global', 'twitter', value), 'url', 'https://twitter.com/username')}
                {renderInputField('YouTube URL', brandingData?.global?.youtube || '', (value) => handleContentChange('global', 'youtube', value), 'url', 'https://youtube.com/channel')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'theme' && (
          <div className="space-y-6">
            {/* Theme Colors */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Theme Colors</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Primary Color', brandingData?.theme?.colors?.primary || '', (value) => handleNestedContentChange('theme', 'colors.primary', value), 'color')}
                {renderInputField('Secondary Color', brandingData?.theme?.colors?.secondary || '', (value) => handleNestedContentChange('theme', 'colors.secondary', value), 'color')}
                {renderInputField('Accent Color', brandingData?.theme?.colors?.accent || '', (value) => handleNestedContentChange('theme', 'colors.accent', value), 'color')}
                {renderInputField('Background Color', brandingData?.theme?.colors?.background || '', (value) => handleNestedContentChange('theme', 'colors.background', value), 'color')}
                {renderInputField('Text Color', brandingData?.theme?.colors?.text || '', (value) => handleNestedContentChange('theme', 'colors.text', value), 'color')}
                {renderInputField('Secondary Text Color', brandingData?.theme?.colors?.textSecondary || '', (value) => handleNestedContentChange('theme', 'colors.textSecondary', value), 'color')}
              </div>
            </div>

            {/* Theme Fonts */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Typography</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Primary Font', brandingData?.theme?.fonts?.primary || '', (value) => handleNestedContentChange('theme', 'fonts.primary', value), 'text', 'Inter, system-ui, sans-serif')}
                {renderInputField('Secondary Font', brandingData?.theme?.fonts?.secondary || '', (value) => handleNestedContentChange('theme', 'fonts.secondary', value), 'text', 'Inter, system-ui, sans-serif')}
                {renderInputField('Heading Font', brandingData?.theme?.fonts?.heading || '', (value) => handleNestedContentChange('theme', 'fonts.heading', value), 'text', 'Inter, system-ui, sans-serif')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'home' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Hero Section</h3>
              </div>

              <div className="space-y-4">
                {renderImageField('Hero Image', brandingData?.home?.heroImage || '', 'home', 'heroImage', 'Main hero background image (recommended: 1920x1080px)')}
                {renderInputField('Hero Title', brandingData?.home?.heroTitle || '', (value) => handleContentChange('home', 'heroTitle', value))}
                {renderInputField('Hero Subtitle', brandingData?.home?.heroSubtitle || '', (value) => handleContentChange('home', 'heroSubtitle', value), 'text', '', 3)}
              </div>
            </div>

            {/* About Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">About Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('About Title', brandingData?.home?.aboutTitle || '', (value) => handleContentChange('home', 'aboutTitle', value))}
                {renderInputField('About Text', brandingData?.home?.aboutText || '', (value) => handleContentChange('home', 'aboutText', value), 'text', '', 4)}
              </div>
            </div>

            {/* Testimonials Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Testimonials Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Testimonial Heading', brandingData?.home?.testimonialHeading || '', (value) => handleContentChange('home', 'testimonialHeading', value))}
              </div>
            </div>

          </div>
        )}

        {activeBrandingSection === 'services' && (
          <div className="space-y-6">
            {/* Services Page Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Services Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Page Title', brandingData?.services?.pageTitle || '', (value) => handleContentChange('services', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingData?.services?.pageSubtitle || '', (value) => handleContentChange('services', 'pageSubtitle', value))}
                {renderInputField('Page Description', brandingData?.services?.pageDescription || '', (value) => handleContentChange('services', 'pageDescription', value), 'text', '', 3)}
              </div>
            </div>

            {/* Service Types */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Service Types</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Loc Maintenance Title', brandingData?.services?.serviceLocMaintenance || '', (value) => handleContentChange('services', 'serviceLocMaintenance', value))}
                {renderInputField('Loc Maintenance Description', brandingData?.services?.serviceLocMaintenanceDesc || '', (value) => handleContentChange('services', 'serviceLocMaintenanceDesc', value), 'text', '', 2)}
                {renderInputField('Starter Locs Title', brandingData?.services?.serviceStarterLocs || '', (value) => handleContentChange('services', 'serviceStarterLocs', value))}
                {renderInputField('Starter Locs Description', brandingData?.services?.serviceStarterLocsDesc || '', (value) => handleContentChange('services', 'serviceStarterLocsDesc', value), 'text', '', 2)}
                {renderInputField('Loc Styling Title', brandingData?.services?.serviceLocStyling || '', (value) => handleContentChange('services', 'serviceLocStyling', value))}
                {renderInputField('Loc Styling Description', brandingData?.services?.serviceLocStylingDesc || '', (value) => handleContentChange('services', 'serviceLocStylingDesc', value), 'text', '', 2)}
                {renderInputField('Natural Hair Care Title', brandingData?.services?.serviceNaturalHairCare || '', (value) => handleContentChange('services', 'serviceNaturalHairCare', value))}
                {renderInputField('Natural Hair Care Description', brandingData?.services?.serviceNaturalHairCareDesc || '', (value) => handleContentChange('services', 'serviceNaturalHairCareDesc', value), 'text', '', 2)}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'consultation' && (
          <div className="space-y-6">
            {/* Consultation Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Consultation Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Page Title', brandingData?.consultation?.pageTitle || '', (value) => handleContentChange('consultation', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingData?.consultation?.pageSubtitle || '', (value) => handleContentChange('consultation', 'pageSubtitle', value))}
                {renderInputField('Page Description', brandingData?.consultation?.pageDescription || '', (value) => handleContentChange('consultation', 'pageDescription', value), 'text', '', 3)}
                {renderInputField('Form Title', brandingData?.consultation?.formTitle || '', (value) => handleContentChange('consultation', 'formTitle', value))}
                {renderInputField('Form Subtitle', brandingData?.consultation?.formSubtitle || '', (value) => handleContentChange('consultation', 'formSubtitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'shop' && (
          <div className="space-y-6">
            {/* Shop Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Shop Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Page Title', brandingData?.shop?.pageTitle || '', (value) => handleContentChange('shop', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingData?.shop?.pageSubtitle || '', (value) => handleContentChange('shop', 'pageSubtitle', value))}
                {renderInputField('Page Description', brandingData?.shop?.pageDescription || '', (value) => handleContentChange('shop', 'pageDescription', value), 'text', '', 3)}
                {renderInputField('Featured Collection Title', brandingData?.shop?.featuredCollectionTitle || '', (value) => handleContentChange('shop', 'featuredCollectionTitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'auth' && (
          <div className="space-y-6">
            {/* Login Page */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Login Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Page Title', brandingData?.login?.pageTitle || '', (value) => handleContentChange('login', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingData?.login?.pageSubtitle || '', (value) => handleContentChange('login', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Signup Page */}
            <div>
              <div className="flex items-center mb-4">
                <FiUserCheck className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Signup Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Page Title', brandingData?.signup?.pageTitle || '', (value) => handleContentChange('signup', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingData?.signup?.pageSubtitle || '', (value) => handleContentChange('signup', 'pageSubtitle', value))}
                {renderInputField('Agree Terms Text', brandingData?.signup?.agreeTermsText || '', (value) => handleContentChange('signup', 'agreeTermsText', value))}
                {renderInputField('Terms Link Text', brandingData?.signup?.termsLinkText || '', (value) => handleContentChange('signup', 'termsLinkText', value))}
                {renderInputField('And Text', brandingData?.signup?.andText || '', (value) => handleContentChange('signup', 'andText', value))}
                {renderInputField('Privacy Link Text', brandingData?.signup?.privacyLinkText || '', (value) => handleContentChange('signup', 'privacyLinkText', value))}
              </div>
            </div>

            {/* Cart Page */}
            <div>
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Cart & Checkout</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Cart Page Title', brandingData?.cart?.pageTitle || '', (value) => handleContentChange('cart', 'pageTitle', value))}
                {renderInputField('Empty Cart Message', brandingData?.cart?.emptyCartMessage || '', (value) => handleContentChange('cart', 'emptyCartMessage', value))}
                {renderInputField('Free Shipping Threshold', brandingData?.cart?.freeShippingThreshold || '', (value) => handleContentChange('cart', 'freeShippingThreshold', value))}
                {renderInputField('Shipping Calculated Text', brandingData?.cart?.shippingCalculated || '', (value) => handleContentChange('cart', 'shippingCalculated', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'footer' && (
          <div className="space-y-6">
            {/* Footer Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiBarChart className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Footer Content</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Footer Description', brandingData?.footer?.description || '', (value) => handleContentChange('footer', 'description', value), 'text', '', 3)}
                {renderInputField('Copyright Text', brandingData?.footer?.copyrightText || '', (value) => handleContentChange('footer', 'copyrightText', value))}
                {renderInputField('Contact Section Title', brandingData?.footer?.contact || '', (value) => handleContentChange('footer', 'contact', value))}
                {renderInputField('Follow Us Section Title', brandingData?.footer?.followUs || '', (value) => handleContentChange('footer', 'followUs', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'legal' && (
          <div className="space-y-6">
            {/* Privacy Policy */}
            <div>
              <div className="flex items-center mb-4">
                <FiUserCheck className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Privacy Policy</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Privacy Policy Title', brandingData?.legal?.privacyPolicy?.title || '', (value) => handleNestedContentChange('legal', 'privacyPolicy.title', value))}
                {renderInputField('Privacy Policy Content', brandingData?.legal?.privacyPolicy?.content || '', (value) => handleNestedContentChange('legal', 'privacyPolicy.content', value), 'text', 'Enter your privacy policy content here...', 10)}
              </div>
            </div>

            {/* Terms of Service */}
            <div>
              <div className="flex items-center mb-4">
                <FiUserCheck className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Terms of Service</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Terms of Service Title', brandingData?.legal?.termsOfService?.title || '', (value) => handleNestedContentChange('legal', 'termsOfService.title', value))}
                {renderInputField('Terms of Service Content', brandingData?.legal?.termsOfService?.content || '', (value) => handleNestedContentChange('legal', 'termsOfService.content', value), 'text', 'Enter your terms of service content here...', 10)}
              </div>
            </div>

            {/* Copyright Information */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Copyright Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Copyright Text', brandingData?.legal?.copyrightText || '', (value) => handleContentChange('legal', 'copyrightText', value), 'text', '© 2024 Your Company. All rights reserved.')}
                {renderInputField('Company Name', brandingData?.legal?.companyName || '', (value) => handleContentChange('legal', 'companyName', value))}
              </div>
            </div>

            {/* Legal Pages Preview */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Legal Pages Preview</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => window.open('/privacy-policy', '_blank')}
                  className="flex items-center justify-center px-4 py-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 hover:bg-blue-100 transition-colors duration-200"
                >
                  <FiEdit className="w-4 h-4 mr-2" />
                  Preview Privacy Policy
                </button>
                <button
                  onClick={() => window.open('/terms-of-service', '_blank')}
                  className="flex items-center justify-center px-4 py-3 bg-green-50 border border-green-200 rounded-lg text-green-700 hover:bg-green-100 transition-colors duration-200"
                >
                  <FiEdit className="w-4 h-4 mr-2" />
                  Preview Terms of Service
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Email Templates Section */}
        {activeBrandingSection === 'emails' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center mb-4">
                <FiMail className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Email Templates</h3>
              </div>

              {loadingTemplates ? (
                <div className="flex items-center justify-center py-8">
                  <FiRefreshCw className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                  <span className="text-gray-600">Loading email templates...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {emailTemplates.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <FiMail className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>No email templates found. Create your first template to get started.</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {emailTemplates.map((template) => (
                        <div key={template._id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold text-gray-900 capitalize">
                              {template.type.replace('-', ' ')}
                            </h4>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              template.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {template.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3 font-medium">
                            Subject: {template.subject}
                          </p>
                          <div className="text-xs text-gray-500 mb-3">
                            <div dangerouslySetInnerHTML={{
                              __html: template.content.substring(0, 150) + (template.content.length > 150 ? '...' : '')
                            }} />
                          </div>
                          {template.variables && template.variables.length > 0 && (
                            <div className="mb-3">
                              <p className="text-xs text-gray-500 mb-1">Available Variables:</p>
                              <div className="flex flex-wrap gap-1">
                                {template.variables.map((variable, index) => (
                                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                    {variable}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                          <button
                            onClick={() => handleEditEmailTemplate(template)}
                            className="w-full px-3 py-2 bg-indigo-50 text-indigo-700 rounded-lg hover:bg-indigo-100 transition-colors duration-200 text-sm font-medium"
                          >
                            <FiEdit className="w-4 h-4 inline mr-2" />
                            Edit Template
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <FiRefreshCw className="w-6 h-6 animate-spin text-indigo-500" />
            <span className="text-gray-600">Loading branding data...</span>
          </div>
        </div>
      )}

      {/* Branding Modal */}
      <BrandingModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveBranding}
        brandingData={brandingData}
        isEdit={!!brandingData}
      />

      {/* Email Template Modal */}
      <EmailTemplateModal
        isOpen={showEmailTemplateModal}
        onClose={() => {
          setShowEmailTemplateModal(false)
          setEditingEmailTemplate(null)
        }}
        onSave={handleSaveEmailTemplate}
        template={editingEmailTemplate}
        loading={loadingTemplates}
      />
    </div>
  )
}

export default AdminBranding
