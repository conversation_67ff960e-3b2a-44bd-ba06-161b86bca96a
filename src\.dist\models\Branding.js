"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Branding = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const brandingSchema = new mongoose_1.Schema({
    global: {
        siteName: { type: String },
        tagline: { type: String },
        logo: { type: String },
        favicon: { type: String },
        phone: { type: String },
        email: { type: String },
        address: { type: String },
        instagram: { type: String },
        facebook: { type: String },
        twitter: { type: String },
        youtube: { type: String }
    },
    home: {
        heroTitle: { type: String },
        heroSubtitle: { type: String },
        heroImage: { type: String },
        aboutTitle: { type: String },
        aboutText: { type: String },
        featuredServices: [{
                title: { type: String },
                description: { type: String },
                image: { type: String }
            }],
        testimonialHeading: { type: String }
    },
    services: {
        pageTitle: { type: String },
        pageSubtitle: { type: String },
        pageDescription: { type: String },
        serviceCategories: [{
                name: { type: String },
                description: { type: String }
            }],
        serviceLocMaintenance: { type: String },
        serviceLocMaintenanceDesc: { type: String },
        serviceStarterLocs: { type: String },
        serviceStarterLocsDesc: { type: String },
        serviceLocStyling: { type: String },
        serviceLocStylingDesc: { type: String },
        serviceNaturalHairCare: { type: String },
        serviceNaturalHairCareDesc: { type: String }
    },
    shop: {
        pageTitle: { type: String },
        pageSubtitle: { type: String },
        pageDescription: { type: String },
        featuredCollectionTitle: { type: String }
    },
    consultation: {
        pageTitle: { type: String },
        pageSubtitle: { type: String },
        pageDescription: { type: String },
        formTitle: { type: String },
        formSubtitle: { type: String }
    },
    login: {
        pageTitle: { type: String },
        pageSubtitle: { type: String },
        signInButton: { type: String },
        signingInText: { type: String },
        noAccountText: { type: String },
        signUpLink: { type: String },
        forgotPasswordLink: { type: String }
    },
    signup: {
        pageTitle: { type: String },
        pageSubtitle: { type: String },
        createAccountButton: { type: String },
        creatingAccountText: { type: String },
        haveAccountText: { type: String },
        signInLink: { type: String },
        agreeTermsText: { type: String },
        termsLinkText: { type: String },
        andText: { type: String },
        privacyLinkText: { type: String }
    },
    cart: {
        pageTitle: { type: String },
        emptyCartMessage: { type: String },
        freeShippingThreshold: { type: String },
        shippingCalculated: { type: String }
    },
    productDetail: {
        addToCartButton: { type: String },
        quantityLabel: { type: String },
        overviewTab: { type: String },
        ingredientsTab: { type: String },
        reviewsTab: { type: String }
    },
    footer: {
        description: { type: String },
        copyrightText: { type: String },
        quickLinks: {
            type: [{
                    name: { type: String },
                    url: { type: String }
                }],
            required: true
        },
        contact: { type: String },
        followUs: { type: String }
    },
    // Dashboard content
    dashboard: {
        welcomeMessage: { type: String },
        overviewTitle: { type: String },
        appointmentsTitle: { type: String },
        ordersTitle: { type: String },
        favoritesTitle: { type: String },
        profileTitle: { type: String },
        nextAppointment: { type: String },
        recentOrders: { type: String },
        loyaltyTitle: { type: String }
    },
    // Buttons and UI elements
    buttons: {
        bookNow: { type: String },
        shopNow: { type: String },
        learnMore: { type: String },
        viewAll: { type: String },
        continueShopping: { type: String },
        proceedToCheckout: { type: String },
        addToCart: { type: String },
        scheduleConsultation: { type: String },
        writeReview: { type: String }
    },
    // Navigation
    navigation: {
        home: { type: String },
        services: { type: String },
        shop: { type: String },
        consultation: { type: String },
        login: { type: String },
        signup: { type: String },
        dashboard: { type: String }
    },
    // Testimonials and reviews
    testimonials: {
        title: { type: String },
        subtitle: { type: String }
    },
    reviews: {
        title: { type: String }
    },
    // Messages
    messages: {
        loading: { type: String },
        error: { type: String },
        notFound: { type: String },
        comingSoon: { type: String },
        cartShipping: { type: String }
    },
    // Business information (stored in branding for admin editing)
    business: {
        name: { type: String },
        tagline: { type: String },
        description: { type: String },
        phone: { type: String },
        email: { type: String },
        address: {
            street: { type: String },
            city: { type: String },
            state: { type: String },
            zip: { type: String },
            full: { type: String }
        },
        social: {
            instagram: { type: String },
            facebook: { type: String },
            twitter: { type: String }
        },
        hours: {
            monday: { type: String },
            tuesday: { type: String },
            wednesday: { type: String },
            thursday: { type: String },
            friday: { type: String },
            saturday: { type: String },
            sunday: { type: String }
        }
    },
    // Theme settings (stored in branding for admin editing)
    theme: {
        colors: {
            primary: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Primary color must be a valid hex color (e.g., #008000 or #080)'
                }
            },
            secondary: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Secondary color must be a valid hex color (e.g., #f3d016 or #f30)'
                }
            },
            accent: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Accent color must be a valid hex color (e.g., #006600 or #060)'
                }
            },
            background: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Background color must be a valid hex color (e.g., #ffffff or #fff)'
                }
            },
            text: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Text color must be a valid hex color (e.g., #000000 or #000)'
                }
            },
            textSecondary: {
                type: String,
                validate: {
                    validator: function (v) {
                        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
                    },
                    message: 'Text secondary color must be a valid hex color (e.g., #666666 or #666)'
                }
            }
        },
        fonts: {
            primary: { type: String },
            secondary: { type: String },
            heading: { type: String }
        }
    },
    // SEO and site settings (stored in branding for admin editing)
    site: {
        seo: {
            title: { type: String },
            description: { type: String },
            keywords: { type: String }
        },
        features: {
            onlineBooking: { type: Boolean },
            ecommerce: { type: Boolean },
            loyaltyProgram: { type: Boolean },
            giftCards: { type: Boolean },
            reviews: { type: Boolean },
            blog: { type: Boolean }
        }
    },
    // Legal content (Privacy Policy and Terms of Service)
    legal: {
        privacyPolicy: {
            title: { type: String },
            content: { type: String },
            lastUpdated: { type: Date }
        },
        termsOfService: {
            title: { type: String },
            content: { type: String },
            lastUpdated: { type: Date }
        },
        copyrightText: { type: String },
        companyName: { type: String }
    }
}, {
    timestamps: true
});
exports.Branding = mongoose_1.default.model('Branding', brandingSchema);
