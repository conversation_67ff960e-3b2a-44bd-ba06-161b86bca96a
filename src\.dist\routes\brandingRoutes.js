"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const rateLimiter_1 = require("../middleware/rateLimiter");
const router = (0, express_1.Router)();
// GET /api/branding (public)
router.get('/', rateLimiter_1.brandingLimiter, controllers_1.BrandingController.getBrandingContent);
// PUT /api/branding (admin only)
router.put('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BrandingController.updateBrandingContent);
// PUT /api/branding/:section (admin only)
router.put('/:section', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BrandingController.updateBrandingSection);
exports.default = router;
