"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/products/:id/reviews
router.get('/:id/reviews', (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.paginationValidation]), controllers_1.ReviewController.getProductReviews);
// POST /api/products/:id/reviews
router.post('/:id/reviews', auth_1.authenticate, (0, validation_1.validate)(validation_2.createReviewValidation), controllers_1.ReviewController.createReview);
// PUT /api/reviews/:id
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ReviewController.updateReview);
// DELETE /api/reviews/:id
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ReviewController.deleteReview);
exports.default = router;
