"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const rateLimiter_1 = require("../middleware/rateLimiter");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// POST /api/auth/register
router.post('/register', rateLimiter_1.authLimiter, (0, validation_1.validate)(validation_2.registerValidation), controllers_1.AuthController.register);
// POST /api/auth/login
router.post('/login', rateLimiter_1.authLimiter, (0, validation_1.validate)(validation_2.loginValidation), controllers_1.AuthController.login);
// POST /api/auth/forgot-password
router.post('/forgot-password', rateLimiter_1.passwordResetLimiter, (0, validation_1.validate)(validation_2.forgotPasswordValidation), controllers_1.AuthController.forgotPassword);
// POST /api/auth/reset-password
router.post('/reset-password', rateLimiter_1.passwordResetLimiter, controllers_1.AuthController.resetPassword);
// GET /api/auth/verify
router.get('/verify', auth_1.authenticate, controllers_1.AuthController.verify);
// POST /api/auth/logout
router.post('/logout', auth_1.authenticate, controllers_1.AuthController.logout);
exports.default = router;
