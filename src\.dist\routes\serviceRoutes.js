"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/services
router.get('/', controllers_1.ServiceController.getAllServices);
// GET /api/services/categories
router.get('/categories', controllers_1.ServiceController.getServiceCategories);
// GET /api/services/:id
router.get('/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceController.getServiceById);
// Admin routes
// POST /api/services (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ServiceController.createService);
// PUT /api/services/:id (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceController.updateService);
// DELETE /api/services/:id (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceController.deleteService);
exports.default = router;
