import { useState, useEffect } from 'react'
import { FiArrowUp } from 'react-icons/fi'

const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false)

  // Show button when page is scrolled down
  useEffect(() => {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      console.log('ScrollToTop: Window is undefined (SSR)')
      return
    }

    const toggleVisibility = () => {
      try {
        // Use modern scrollY instead of deprecated pageYOffset
        const scrolled = window.scrollY || window.pageYOffset || document.documentElement.scrollTop || 0

        if (scrolled > 300) {
          setIsVisible(true)
        } else {
          setIsVisible(false)
        }
      } catch (error) {
        console.error('ScrollToTop: Error in toggleVisibility:', error)
      }
    }

    // Check initial scroll position
    toggleVisibility()

    window.addEventListener('scroll', toggleVisibility, { passive: true })
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  // Scroll to top smoothly
  const scrollToTop = () => {
    if (typeof window === 'undefined') return

    try {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    } catch (error) {
      // Fallback for older browsers
      window.scrollTo(0, 0)
    }
  }

  // Debug logging for development only
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('ScrollToTop component mounted, isVisible:', isVisible)
    }
  }, [isVisible])

  // Show button based on scroll position
  const showButton = isVisible

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '24px',
        right: '24px',
        zIndex: 9999,
        display: showButton ? 'block' : 'none'
      }}
    >
      <button
        onClick={scrollToTop}
        style={{
          backgroundColor: '#eab308',
          color: 'white',
          padding: '12px',
          borderRadius: '50%',
          border: 'none',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          width: '48px',
          height: '48px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = '#ca8a04'
          e.target.style.transform = 'scale(1.1)'
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = '#eab308'
          e.target.style.transform = 'scale(1)'
        }}
        aria-label="Scroll to top"
        title="Scroll to top"
      >
        <FiArrowUp style={{ width: '24px', height: '24px' }} />
      </button>
    </div>
  )
}

export default ScrollToTop
