import { useState, useMemo } from 'react'
import { FiPackage, FiSearch, FiFilter, FiEye, FiEdit3, FiTrash2, FiPlus } from 'react-icons/fi'
import ProductModal from '../../../components/Modals/ProductModal'
import ProductDetailModal from '../../../components/Modals/ProductDetailModal'
import { adminService } from '../../../services'
import { useDebouncedSearch } from '../../../hooks/useDebounce'
import { searchProducts } from '../../../utils/searchUtils'
import { useApiWithToast, TOAST_MESSAGES } from '../../../utils/apiWithToast'

const AdminProducts = ({
  products,
  sectionLoading,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  showAddModal,
  setShowAddModal,
  modalType,
  setModalType,
  editingItem,
  setEditingItem,
  setViewingItem,
  setConfirmDialog,
  handleDeleteProduct,
  branding,
  refreshData
}) => {
  // Toast notifications
  const { executeWithToast } = useApiWithToast()

  // State for detail modal
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [detailItem, setDetailItem] = useState(null)

  // Ensure products is always an array
  const productsArray = Array.isArray(products)
    ? products
    : (products?.products && Array.isArray(products.products))
      ? products.products
    : (products?.data && Array.isArray(products.data))
      ? products.data
      : []

  // Use debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 150)

  // Use memoized filtering for better performance
  const filteredProducts = useMemo(() => {
    // First apply search filter using the comprehensive search utility
    let filtered = searchProducts(productsArray, debouncedSearchTerm)

    // Then apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(product => product.status === statusFilter)
    }

    return filtered
  }, [productsArray, debouncedSearchTerm, statusFilter])

  // Handlers for detail modal
  const handleViewDetails = (product) => {
    setDetailItem(product)
    setShowDetailModal(true)
  }

  const handleEditFromDetail = (product) => {
    setShowDetailModal(false)
    setEditingItem(product)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteFromDetail = async (productId) => {
    await handleDeleteProduct(productId)
    setShowDetailModal(false)
    refreshData()
  }

  const getProductStatus = (stock) => {
    if (stock === 0) return 'out-of-stock'
    if (stock <= 5) return 'low-stock'
    return 'in-stock'
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'in-stock':
        return 'bg-green-100 text-green-800'
      case 'low-stock':
        return 'bg-yellow-100 text-yellow-800'
      case 'out-of-stock':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status) => {
    switch (status) {
      case 'in-stock':
        return 'In Stock'
      case 'low-stock':
        return 'Low Stock'
      case 'out-of-stock':
        return 'Out of Stock'
      default:
        return 'Unknown'
    }
  }

  const handleSaveProduct = async (productData) => {
    if (editingItem) {
      await executeWithToast(
        () => adminService.updateProduct(editingItem._id || editingItem.id, productData),
        {
          loadingMessage: 'Updating product...',
          successMessage: TOAST_MESSAGES.UPDATE_SUCCESS,
          errorMessage: 'Failed to update product',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            if (refreshData) refreshData()
          }
        }
      )
    } else {
      await executeWithToast(
        () => adminService.createProduct(productData),
        {
          loadingMessage: 'Creating product...',
          successMessage: TOAST_MESSAGES.CREATE_SUCCESS,
          errorMessage: 'Failed to create product',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            if (refreshData) refreshData()
          }
        }
      )
    }
  }

  const handleEditProduct = (product) => {
    setEditingItem(product)
    setModalType('edit')
    setShowAddModal(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Products Management</h2>
          <p className="text-gray-600 mt-1">Manage your product inventory and catalog</p>
        </div>
        <button
          onClick={() => {
            setShowAddModal(true)
            setModalType('add')
            setEditingItem(null)
          }}
          className="flex items-center justify-center px-4 sm:px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] w-full sm:w-auto text-sm sm:text-base"
        >
          <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
          <span className="hidden xs:inline">Add </span>Product
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products by name, description, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Status</option>
                <option value="in-stock">In Stock</option>
                <option value="low-stock">Low Stock</option>
                <option value="out-of-stock">Out of Stock</option>
              </select>
            </div>
          </div>
        </div>

        {sectionLoading?.products ? (
          <div className="overflow-x-auto table-scroll">
            <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '900px' }}>
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '250px' }}>
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '80px' }}>
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '80px' }}>
                    Sold
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[...Array(5)].map((_, index) => (
                  <tr key={index} className="animate-pulse">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gray-300 rounded-lg"></div>
                        <div className="ml-4 space-y-2">
                          <div className="h-4 bg-gray-300 rounded w-24"></div>
                          <div className="h-3 bg-gray-300 rounded w-32"></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-12"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-8"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-6 bg-gray-300 rounded-full w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-8"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex justify-end space-x-2">
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="overflow-x-auto table-scroll">
            <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sold
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <FiPackage className="w-6 h-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                    {product.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="font-semibold">${product.price?.toFixed(2) || '0.00'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className={`font-medium ${
                      product.stock <= 5 ? 'text-red-600' : 
                      product.stock <= 20 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {product.stock || 0}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(getProductStatus(product.stock))}`}>
                      {getStatusLabel(getProductStatus(product.stock))}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.sold || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2 mobile-safe-buttons">
                      <button
                        onClick={() => handleViewDetails(product)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="View Details"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Edit"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setConfirmDialog({
                            title: 'Delete Product',
                            message: `Are you sure you want to delete "${product.name}"?`,
                            onConfirm: () => handleDeleteProduct(product.id || index)
                          })
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Delete"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        ) : (
          <div className="text-center py-12">
            <FiPackage className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first product.'
              }
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <button
                onClick={() => {
                  setShowAddModal(true)
                  setModalType('add')
                  setEditingItem(null)
                }}
                className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 cursor-pointer"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                Add First Product
              </button>
            )}
          </div>
        )}
      </div>

      {/* Product Modal */}
      <ProductModal
        isOpen={showAddModal && (modalType === 'add' || modalType === 'edit')}
        onClose={() => {
          setShowAddModal(false)
          setEditingItem(null)
          setModalType('')
        }}
        onSave={handleSaveProduct}
        editingItem={editingItem}
        modalType={modalType}
        branding={branding}
      />

      {/* Product Detail Modal */}
      <ProductDetailModal
        product={detailItem}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />
    </div>
  )
}

export default AdminProducts
