"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authRoutes_1 = __importDefault(require("./authRoutes"));
const appointmentRoutes_1 = __importDefault(require("./appointmentRoutes"));
const consultationRoutes_1 = __importDefault(require("./consultationRoutes"));
const serviceRoutes_1 = __importDefault(require("./serviceRoutes"));
const productRoutes_1 = __importDefault(require("./productRoutes"));
const cartRoutes_1 = __importDefault(require("./cartRoutes"));
const orderRoutes_1 = __importDefault(require("./orderRoutes"));
const userRoutes_1 = __importDefault(require("./userRoutes"));
const reviewRoutes_1 = __importDefault(require("./reviewRoutes"));
const notificationRoutes_1 = __importDefault(require("./notificationRoutes"));
const adminRoutes_1 = __importDefault(require("./adminRoutes"));
const brandingRoutes_1 = __importDefault(require("./brandingRoutes"));
const paymentRoutes_1 = __importDefault(require("./paymentRoutes"));
const businessRoutes_1 = __importDefault(require("./businessRoutes"));
const contentRoutes_1 = __importDefault(require("./contentRoutes"));
const testimonialRoutes_1 = __importDefault(require("./testimonialRoutes"));
const discountRoutes_1 = __importDefault(require("./discountRoutes"));
const analyticsRoutes_1 = __importDefault(require("./analyticsRoutes"));
const uploadRoutes_1 = __importDefault(require("./uploadRoutes"));
const seoRoutes_1 = __importDefault(require("./seoRoutes"));
const userRoleRoutes_1 = __importDefault(require("./userRoleRoutes"));
const giftCardRoutes_1 = __importDefault(require("./giftCardRoutes"));
const loyaltyRoutes_1 = __importDefault(require("./loyaltyRoutes"));
const extendedRoutes_1 = __importDefault(require("./extendedRoutes"));
const categoryRoutes_1 = __importDefault(require("./categoryRoutes"));
const emailRoutes_1 = __importDefault(require("./emailRoutes"));
const controllers_1 = require("../controllers");
const rateLimiter_1 = require("../middleware/rateLimiter");
const router = (0, express_1.Router)();
// Mount all routes
router.use('/auth', authRoutes_1.default);
router.use('/appointments', appointmentRoutes_1.default);
router.use('/consultation', consultationRoutes_1.default);
router.use('/services', serviceRoutes_1.default);
router.use('/products', productRoutes_1.default);
router.use('/cart', cartRoutes_1.default);
router.use('/orders', orderRoutes_1.default);
router.use('/users', userRoutes_1.default);
router.use('/products', reviewRoutes_1.default); // Reviews are nested under products
router.use('/notifications', notificationRoutes_1.default);
router.use('/admin', adminRoutes_1.default);
router.use('/branding', brandingRoutes_1.default);
router.use('/categories', categoryRoutes_1.default);
// Aggregated branding endpoint (single request for all branding data)
router.get('/branding/complete', rateLimiter_1.brandingLimiter, controllers_1.BrandingController.getCompleteBranding);
// Individual branding endpoints (kept for backward compatibility)
router.get('/business-profile', rateLimiter_1.brandingLimiter, controllers_1.BrandingController.getBusinessProfile);
router.get('/theme-settings', rateLimiter_1.brandingLimiter, controllers_1.BrandingController.getThemeSettings);
router.get('/site-settings', rateLimiter_1.brandingLimiter, controllers_1.BrandingController.getSiteSettings);
router.use('/admin', paymentRoutes_1.default);
router.use('/', businessRoutes_1.default); // Business hours at root level
router.use('/admin', businessRoutes_1.default); // Admin business routes
router.use('/content', contentRoutes_1.default);
router.use('/testimonials', testimonialRoutes_1.default);
router.use('/discount-codes', discountRoutes_1.default);
router.use('/admin/discount-codes', discountRoutes_1.default);
router.use('/admin/analytics', analyticsRoutes_1.default);
router.use('/upload', uploadRoutes_1.default);
router.use('/admin/seo', seoRoutes_1.default);
router.use('/admin/users', userRoleRoutes_1.default);
router.use('/gift-cards', giftCardRoutes_1.default);
router.use('/users/loyalty', loyaltyRoutes_1.default);
router.use('/loyalty', loyaltyRoutes_1.default);
router.use('/admin/emails', emailRoutes_1.default);
router.use('/', extendedRoutes_1.default); // All extended routes
// Health check endpoint
router.get('/health', (_req, res) => {
    res.json({
        success: true,
        message: 'API is running',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});
exports.default = router;
