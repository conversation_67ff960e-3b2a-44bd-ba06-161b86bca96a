
import { FiCalendar, FiShoppingBag, FiStar, FiArrowRight, FiUser, FiSettings } from 'react-icons/fi'
import { useBranding } from '../contexts/BrandingContext'
import OptimizedImage from '../components/OptimizedImage'

const Home = ({ onNavigate }) => {
  const { branding, isLoading } = useBranding()

  // Show loading state when branding is not available
  if (isLoading || !branding) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  const features = [
    {
      icon: <FiCalendar className="w-8 h-8" />,
      title: "Easy Booking",
      description: "Schedule your consultation with just a few clicks"
    },
    {
      icon: <FiStar className="w-8 h-8" />,
      title: "Expert Care",
      description: "10+ years of professional locs and natural hair experience"
    },
    {
      icon: <FiShoppingBag className="w-8 h-8" />,
      title: "Quality Products",
      description: "Premium hair care products for maintaining healthy locs"
    }
  ]

  const services = [
    {
      name: "Micro Locs Installation",
      description: "Precision micro locs for a natural, versatile look",
      price: "Starting at $300",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      name: "Loc Maintenance",
      description: "Regular maintenance to keep your locs healthy and neat",
      price: "Starting at $80",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      name: "Consultation",
      description: "Personalized consultation to determine the best loc style for you",
      price: "$50",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-yellow-50 to-yellow-100 py-12 sm:py-16 lg:py-32" aria-label="Hero section with main call-to-action">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                {branding?.content?.heroTitle || branding?.content?.home?.heroTitle || 'Transform Your Hair with Professional Care'}
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 mb-6 lg:mb-8 leading-relaxed max-w-lg mx-auto lg:mx-0">
                {branding?.content?.heroSubtitle || branding?.content?.home?.heroSubtitle || 'Expert hair services and premium products for your hair journey'}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 max-w-md mx-auto lg:mx-0">
                <button
                  onClick={() => onNavigate('consultation')}
                  className="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium text-white rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
                  style={{ backgroundColor: branding?.colors?.secondary || '#1F2937' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = branding?.colors?.accent || '#F59E0B'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = branding?.colors?.secondary || '#1F2937'}
                  aria-label="Book a consultation appointment"
                >
                  <span className="hidden sm:inline">{branding?.content?.buttons?.scheduleConsultation || 'Book Consultation'}</span>
                  <span className="sm:hidden">Book Now</span>
                  <FiCalendar className="ml-2 w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
                </button>
                <button
                  onClick={() => onNavigate('shop')}
                  className="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium bg-white rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
                  style={{ color: branding?.colors?.secondary || '#1F2937', borderColor: branding?.colors?.secondary || '#1F2937', borderWidth: '2px' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
                  aria-label="Browse hair care products"
                >
                  <span className="hidden sm:inline">{branding?.content?.buttons?.shopNow || 'Shop Products'}</span>
                  <span className="sm:hidden">Shop</span>
                  <FiShoppingBag className="ml-2 w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
            <div className="relative mt-8 lg:mt-0">
              <div className="aspect-square max-w-md mx-auto lg:max-w-none rounded-2xl lg:rounded-3xl overflow-hidden shadow-xl lg:shadow-2xl" style={{ background: `linear-gradient(to bottom right, ${branding?.colors?.secondary || '#1F2937'}40, ${branding?.colors?.secondary || '#1F2937'}60)` }}>
                {(branding?.images?.hero || branding?.content?.home?.heroImage) ? (
                  <OptimizedImage
                    src={branding?.images?.hero || branding?.content?.home?.heroImage}
                    alt="Beautiful locs hairstyle showcasing professional loc installation and styling"
                    className="w-full h-full object-cover"
                    lazy={false}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400 text-lg">Hero Image</span>
                  </div>
                )}
              </div>
              <div className="absolute -bottom-4 -left-4 lg:-bottom-6 lg:-left-6 bg-white p-3 lg:p-6 rounded-xl lg:rounded-2xl shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="flex" style={{ color: '#FFFF00' }}>
                    {[...Array(5)].map((_, i) => (
                      <FiStar key={i} className="w-4 h-4 lg:w-5 lg:h-5 fill-current" />
                    ))}
                  </div>
                  <span className="text-gray-600 font-medium text-sm lg:text-base">500+ Happy Clients</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 sm:py-16 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              Why Choose {branding?.businessName || branding?.content?.business?.name || 'Us'}?
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {branding?.description || branding?.content?.business?.description || 'Professional hair care services with years of experience and dedication to excellence.'}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6 lg:p-8 rounded-2xl bg-gray-50 hover:bg-yellow-50 transition-all duration-200 hover:shadow-lg cursor-pointer">
                <div className="inline-flex items-center justify-center w-14 h-14 lg:w-16 lg:h-16 bg-yellow-100 rounded-full mb-4 lg:mb-6" style={{ color: branding?.colors?.secondary || '#1F2937' }}>
                  {feature.icon}
                </div>
                <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-3 lg:mb-4">{feature.title}</h3>
                <p className="text-gray-600 text-sm lg:text-base">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-12 sm:py-16 lg:py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {branding?.content?.servicesTitle || branding?.content?.services?.pageTitle || 'Our Services'}
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {branding?.content?.servicesSubtitle || branding?.content?.services?.pageSubtitle || 'Professional hair care services tailored to your needs'}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 cursor-pointer">
                <div className="aspect-video bg-gray-200">
                  <OptimizedImage
                    src={service.image}
                    alt={`${service.name} - Professional hair service showcasing ${service.description.toLowerCase()}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-4 sm:p-6">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 mb-4 text-sm sm:text-base">{service.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold" style={{ color: branding?.colors?.primary || '#3B82F6' }}>{service.price}</span>
                    <button
                      onClick={() => onNavigate('consultation')}
                      className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer hover:shadow-md transform hover:-translate-y-0.5"
                      style={{
                        color: 'white',
                        backgroundColor: branding?.colors?.primary || '#3B82F6'
                      }}
                      onMouseEnter={(e) => e.target.style.backgroundColor = branding?.colors?.accent || '#F59E0B'}
                      onMouseLeave={(e) => e.target.style.backgroundColor = branding?.colors?.primary || '#3B82F6'}
                    >
                      Book Now
                      <FiArrowRight className="ml-1 w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <button
              onClick={() => onNavigate('services')}
              className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium bg-white rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
              style={{ color: branding?.colors?.secondary || '#1F2937', borderColor: branding?.colors?.secondary || '#1F2937', borderWidth: '2px' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
            >
              View All Services
              <FiArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20" style={{ backgroundColor: branding?.colors?.secondary || '#1F2937' }}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight">
            Ready to Transform Your Hair?
          </h2>
          <p className="text-lg sm:text-xl text-yellow-100 mb-8 max-w-2xl mx-auto leading-relaxed">
            Book your consultation today and let's create the perfect locs for your lifestyle
          </p>
          <button
            onClick={() => onNavigate('consultation')}
            className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium bg-white rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
            style={{ color: branding?.colors?.secondary || '#1F2937' }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
          >
            <span className="hidden sm:inline">Schedule Consultation</span>
            <span className="sm:hidden">Book Now</span>
            <FiCalendar className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>
      </section>
    </div>
  )
}

export default Home
