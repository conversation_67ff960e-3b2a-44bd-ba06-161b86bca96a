"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mongoIdValidation = exports.paginationValidation = exports.createReviewValidation = exports.createOrderValidation = exports.updateCartItemValidation = exports.addToCartValidation = exports.updateAppointmentValidation = exports.createAppointmentValidation = exports.forgotPasswordValidation = exports.loginValidation = exports.registerValidation = void 0;
const express_validator_1 = require("express-validator");
// Auth validation
exports.registerValidation = [
    (0, express_validator_1.body)('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('phone')
        .optional()
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long'),
    (0, express_validator_1.body)('confirmPassword')
        .custom((value, { req }) => {
        if (value !== req.body.password) {
            throw new Error('Passwords do not match');
        }
        return true;
    })
];
exports.loginValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('Password is required')
];
exports.forgotPasswordValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
];
// Appointment validation
exports.createAppointmentValidation = [
    (0, express_validator_1.body)('service')
        .isMongoId()
        .withMessage('Please provide a valid service ID'),
    (0, express_validator_1.body)('date')
        .isISO8601()
        .toDate()
        .withMessage('Please provide a valid date'),
    (0, express_validator_1.body)('time')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Please provide a valid time in HH:MM format'),
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('phone')
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number'),
    (0, express_validator_1.body)('message')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Message cannot exceed 500 characters')
];
exports.updateAppointmentValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Please provide a valid appointment ID'),
    (0, express_validator_1.body)('date')
        .optional()
        .isISO8601()
        .toDate()
        .withMessage('Please provide a valid date'),
    (0, express_validator_1.body)('time')
        .optional()
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Please provide a valid time in HH:MM format'),
    (0, express_validator_1.body)('status')
        .optional()
        .isIn(['pending', 'confirmed', 'completed', 'cancelled'])
        .withMessage('Invalid status')
];
// Cart validation
exports.addToCartValidation = [
    (0, express_validator_1.body)('productId')
        .isMongoId()
        .withMessage('Please provide a valid product ID'),
    (0, express_validator_1.body)('quantity')
        .isInt({ min: 1, max: 100 })
        .withMessage('Quantity must be between 1 and 100')
];
exports.updateCartItemValidation = [
    (0, express_validator_1.param)('itemId')
        .isMongoId()
        .withMessage('Please provide a valid item ID'),
    (0, express_validator_1.body)('quantity')
        .isInt({ min: 1, max: 100 })
        .withMessage('Quantity must be between 1 and 100')
];
// Order validation
exports.createOrderValidation = [
    (0, express_validator_1.body)('items')
        .isArray({ min: 1 })
        .withMessage('Order must contain at least one item'),
    (0, express_validator_1.body)('items.*.productId')
        .isMongoId()
        .withMessage('Please provide valid product IDs'),
    (0, express_validator_1.body)('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('Quantity must be at least 1'),
    (0, express_validator_1.body)('shippingAddress.street')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Street address must be between 5 and 200 characters'),
    (0, express_validator_1.body)('shippingAddress.city')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('City must be between 2 and 50 characters'),
    (0, express_validator_1.body)('shippingAddress.state')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State must be between 2 and 50 characters'),
    (0, express_validator_1.body)('shippingAddress.zip')
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Please provide a valid ZIP code'),
    (0, express_validator_1.body)('paymentMethod')
        .isIn(['credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery'])
        .withMessage('Invalid payment method')
];
// Review validation
exports.createReviewValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Please provide a valid product ID'),
    (0, express_validator_1.body)('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),
    (0, express_validator_1.body)('comment')
        .trim()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Comment must be between 10 and 1000 characters')
];
// Query validation
exports.paginationValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
];
const mongoIdValidation = (field = 'id') => [
    (0, express_validator_1.param)(field)
        .isMongoId()
        .withMessage(`Please provide a valid ${field}`)
];
exports.mongoIdValidation = mongoIdValidation;
