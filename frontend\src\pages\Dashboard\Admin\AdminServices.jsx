import { useState, useMemo } from 'react'
import { FiSettings, FiSearch, FiFilter, FiEye, FiEdit3, FiTrash2, FiPlus } from 'react-icons/fi'
import ServiceModal from '../../../components/Modals/ServiceModal'
import ServiceDetailModal from '../../../components/Modals/ServiceDetailModal'
import { adminService } from '../../../services'
import { useDebouncedSearch } from '../../../hooks/useDebounce'
import { searchServices } from '../../../utils/searchUtils'
import { useApiWithToast, TOAST_MESSAGES } from '../../../utils/apiWithToast'

const AdminServices = ({
  services,
  sectionLoading,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  showAddModal,
  setShowAddModal,
  modalType,
  setModalType,
  editingItem,
  setEditingItem,
  setViewingItem,
  setConfirmDialog,
  handleDeleteService,
  branding,
  refreshData
}) => {
  // Toast notifications
  const { executeWithToast } = useApiWithToast()

  // State for detail modal
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [detailItem, setDetailItem] = useState(null)

  // Ensure services is always an array
  const servicesArray = Array.isArray(services)
    ? services
    : (services?.services && Array.isArray(services.services))
      ? services.services
    : (services?.data && Array.isArray(services.data))
      ? services.data
      : []

  // Use debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 150)

  // Use memoized filtering for better performance
  const filteredServices = useMemo(() => {
    // First apply search filter using the comprehensive search utility
    let filtered = searchServices(servicesArray, debouncedSearchTerm)

    // Then apply status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'active') {
        filtered = filtered.filter(service => service.isActive)
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(service => !service.isActive)
      }
    }

    return filtered
  }, [servicesArray, debouncedSearchTerm, statusFilter])

  // Handlers for detail modal
  const handleViewDetails = (service) => {
    setDetailItem(service)
    setShowDetailModal(true)
  }

  const handleEditFromDetail = (service) => {
    setShowDetailModal(false)
    setEditingItem(service)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteFromDetail = async (serviceId) => {
    await handleDeleteService(serviceId)
    setShowDetailModal(false)
    refreshData()
  }

  const handleSaveService = async (serviceData) => {
    if (editingItem) {
      await executeWithToast(
        () => adminService.updateService(editingItem.id, serviceData),
        {
          loadingMessage: 'Updating service...',
          successMessage: TOAST_MESSAGES.UPDATE_SUCCESS,
          errorMessage: 'Failed to update service',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            if (refreshData) refreshData()
          }
        }
      )
    } else {
      await executeWithToast(
        () => adminService.createService(serviceData),
        {
          loadingMessage: 'Creating service...',
          successMessage: TOAST_MESSAGES.CREATE_SUCCESS,
          errorMessage: 'Failed to create service',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            if (refreshData) refreshData()
          }
        }
      )
    }
  }

  const handleEditService = (service) => {
    setEditingItem(service)
    setModalType('service')
    setShowAddModal(true)
  }

  const handleViewService = (service) => {
    setViewingItem(service)
    setModalType('view')
    setShowAddModal(true)
  }

  const handleDeleteClick = (service) => {
    setConfirmDialog({
      title: 'Delete Service',
      message: `Are you sure you want to delete "${service.name}"? This action cannot be undone.`,
      onConfirm: () => handleDeleteService(service._id || service.id)
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Services Management</h2>
          <p className="text-gray-600 mt-1">Manage your service offerings and pricing</p>
        </div>
        <button
          onClick={() => {
            setEditingItem(null)
            setModalType('service')
            setShowAddModal(true)
          }}
          className="flex items-center justify-center px-4 sm:px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] w-full sm:w-auto text-sm sm:text-base"
        >
          <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
          <span className="hidden xs:inline">Add </span>Service
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          <div className="relative">
            <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="pl-10 pr-8 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 cursor-pointer"
            >
              <option value="all">All Services</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      {sectionLoading?.services ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-full mb-1"></div>
                  <div className="h-4 bg-gray-300 rounded w-2/3"></div>
                </div>
                <div className="h-6 bg-gray-300 rounded-full w-16"></div>
              </div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                <div className="flex gap-2 mt-4">
                  <div className="h-8 bg-gray-300 rounded flex-1"></div>
                  <div className="h-8 bg-gray-300 rounded flex-1"></div>
                  <div className="h-8 bg-gray-300 rounded w-8"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.length > 0 ? (
          filteredServices.map((service) => (
            <div
              key={service._id || service.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">{service.description}</p>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  service.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {service.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-semibold text-gray-900">${service.price}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Duration:</span>
                  <span className="font-semibold text-gray-900">{service.duration} min</span>
                </div>
                {service.category && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <span className="font-semibold text-gray-900">{service.category}</span>
                  </div>
                )}
              </div>

              <div className="flex gap-1 sm:gap-2">
                <button
                  onClick={() => handleViewDetails(service)}
                  className="flex-1 flex items-center justify-center px-2 sm:px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-all duration-200 cursor-pointer text-xs sm:text-sm"
                >
                  <FiEye className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  <span className="hidden xs:inline">View</span>
                  <span className="xs:hidden">👁</span>
                </button>
                <button
                  onClick={() => handleEditService(service)}
                  className="flex-1 flex items-center justify-center px-2 sm:px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-all duration-200 cursor-pointer text-xs sm:text-sm"
                >
                  <FiEdit3 className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  <span className="hidden xs:inline">Edit</span>
                  <span className="xs:hidden">✏</span>
                </button>
                <button
                  onClick={() => handleDeleteClick(service)}
                  className="flex-1 flex items-center justify-center px-2 sm:px-3 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-all duration-200 cursor-pointer text-xs sm:text-sm"
                >
                  <FiTrash2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  <span className="hidden xs:inline">Delete</span>
                  <span className="xs:hidden">🗑</span>
                </button>
              </div>
            </div>
          ))
          ) : (
          <div className="col-span-full">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-white/20 text-center">
              <FiSettings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Services Found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || statusFilter !== 'all' 
                  ? 'No services match your current filters.' 
                  : 'Start by adding your first service offering.'}
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <button
                  onClick={() => {
                    setEditingItem(null)
                    setModalType('service')
                    setShowAddModal(true)
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                >
                  <FiPlus className="w-5 h-5 mr-2 inline" />
                  Add Your First Service
                </button>
              )}
            </div>
          </div>
          )}
        </div>
      )}

      {/* Service Modal */}
      {showAddModal && modalType === 'service' && (
        <ServiceModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false)
            setEditingItem(null)
            setModalType('')
          }}
          onSave={handleSaveService}
          service={editingItem}
          branding={branding}
        />
      )}

      {/* View Service Modal */}
      {showAddModal && modalType === 'view' && (
        <ServiceModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false)
            setViewingItem(null)
            setModalType('')
          }}
          service={editingItem}
          viewOnly={true}
          branding={branding}
        />
      )}

      {/* Service Detail Modal */}
      <ServiceDetailModal
        service={detailItem}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />
    </div>
  )
}

export default AdminServices
